--- Usecases
- Captcha
- Streaming the social login flow
- Streaming whatever happened on the browser.. - live view for the user/admin


-- create 2 tabs
    - create control tab : (about:blank) -> cdp sessions -> every other scripts that we need.. <<<<<---

    - create target tab -- Only need to inject the tab-streamer-script
        
-- found a captcha?
    - inject the control tab script --- CONTROL TAB
        - ontrack event--
    - inject captcha detector scripts --- CONTROL TAB
    - inject captcha detector tensorflow scripts --- CONTROL TAB
    - inject screen cropping interceptor --- CONTROL TAB
    - inject CDP manager script --- CONTROL TAB
    - inject target tab streamer scripts (starts the stream) --- TARGET TAB
        - init() -> websocket -> webRTC -> it establishes the webRTC 
          connection to the control tab.. -> getDisplayMedia()-> stream->



-- notify UI

---

---

-- create 2 tabs - create control tab - inject the control tab script

    - create target tab

-- social login button clicked? - inject target tab scripts (starts the stream) - inject CDP manager script - inject target tab streamer scripts (starts the stream)

-- notify UI

-- creates 2 tabs - create control tab - inject the persistent-cdp-controller - inject the cross-tab-communicator - initializing the cross-tab-communicator

    - create target tab
        - inject browser-controller-proxy

\***\*\_\_\_\*\***. \***\*\_\_\_\*\***
|control tab| |target tab|

```. ~~~~~~~~~~~~
 ____________________|
|client|

--??

---------- we have to take a screenshot (grayscaled /// redacted content) ---------

-- Now we detect a captcha
-- We plan to stream..
```
